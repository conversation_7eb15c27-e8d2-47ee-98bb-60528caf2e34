package routes

import (
	"github.com/adc-credit/backend/internal/handlers"
	"github.com/adc-credit/backend/internal/middleware"
	"github.com/gin-gonic/gin"
)

// RegisterShopRoutes registers all routes related to the unified shop system
func RegisterShopRoutes(r *gin.Engine) {
	// Protected routes (require authentication)
	protected := r.Group("/api/v1")
	protected.Use(middleware.AuthMiddleware())
	{
		// Unified shop management
		shops := protected.Group("/shops")
		{
			// Shop CRUD operations
			shops.GET("", handlers.GetShops)
			shops.POST("", handlers.CreateShop)
			shops.GET("/:id", handlers.GetShop)
			shops.GET("/slug/:slug", handlers.GetShopBySlug)
			shops.PUT("/:id", handlers.UpdateShop)
			shops.DELETE("/:id", handlers.DeleteShop)

			// Shop branch management
			shops.GET("/branches", handlers.GetShopBranches)         // ?shop_id=uuid
			shops.POST("/branches", handlers.CreateShopBranch)       // ?shop_id=uuid
			shops.GET("/branches/:id", handlers.GetShopBranch)       // ?shop_id=uuid
			shops.PUT("/branches/:id", handlers.UpdateShopBranch)    // ?shop_id=uuid
			shops.DELETE("/branches/:id", handlers.DeleteShopBranch) // ?shop_id=uuid

			// Customer management (for retail shops)
			shops.GET("/:id/customers", handlers.GetShopCustomers)
			shops.POST("/:id/customers", handlers.AddShopCustomer)
			shops.POST("/:id/customers/:customerId/credits", handlers.AddShopCredit)

			// External user management (for API service shops) - TODO: Implement handlers
			// shops.GET("/:id/external-users", handlers.GetShopExternalUsers)
			// shops.POST("/:id/external-users", handlers.CreateShopExternalUser)
			// shops.GET("/:id/external-users/:userId", handlers.GetShopExternalUser)
			// shops.PUT("/:id/external-users/:userId", handlers.UpdateShopExternalUser)
			// shops.DELETE("/:id/external-users/:userId", handlers.DeleteShopExternalUser)
			// shops.POST("/:id/external-users/:userId/credits/add", handlers.AddCreditsToShopExternalUser)
			// shops.POST("/:id/external-users/:userId/credits/reduce", handlers.ReduceCreditsFromShopExternalUser)

			// API key management (for all shop types) - TODO: Implement handlers
			// shops.GET("/:id/apikeys", handlers.GetShopAPIKeys)
			// shops.POST("/:id/apikeys", handlers.CreateShopAPIKey)
			// shops.GET("/:id/apikeys/:keyId", handlers.GetShopAPIKey)
			// shops.PUT("/:id/apikeys/:keyId", handlers.UpdateShopAPIKey)
			// shops.DELETE("/:id/apikeys/:keyId", handlers.DeleteShopAPIKey)

			// Branch-level API key management - TODO: Implement handlers
			// shops.GET("/branches/:branchId/apikeys", handlers.GetShopBranchAPIKeys)
			// shops.POST("/branches/:branchId/apikeys", handlers.CreateShopBranchAPIKey)
			// shops.GET("/branches/:branchId/apikeys/:keyId", handlers.GetShopBranchAPIKey)
			// shops.PUT("/branches/:branchId/apikeys/:keyId", handlers.UpdateShopBranchAPIKey)
			// shops.DELETE("/branches/:branchId/apikeys/:keyId", handlers.DeleteShopBranchAPIKey)

			// Credit code management (for retail shops)
			shops.GET("/:id/credit-codes", handlers.GetCreditCodes)
			shops.POST("/:id/credit-codes", handlers.GenerateCreditCode)
			shops.POST("/:id/credit-codes/qr", handlers.GenerateQRCode)

			// Transaction management
			shops.GET("/:id/transactions", handlers.GetShopTransactions)

			// Shop statistics - TODO: Implement handler
			// shops.GET("/:id/stats", handlers.GetShopStats)
		}

		// Customer routes (for interacting with shops as a customer)
		customers := protected.Group("/customer")
		{
			customers.GET("/shops", handlers.GetCustomerShops)
			customers.GET("/shops/:id", handlers.GetCustomerShop)
			customers.GET("/shops/:id/transactions", handlers.GetCustomerTransactions)
			customers.POST("/shops/:id/use-credit", handlers.UseShopCredit)
			customers.POST("/redeem-code", handlers.RedeemCreditCode)
		}

		// Legacy routes for backward compatibility (will be deprecated)
		// Merchant shop routes (redirect to unified shop routes)
		merchantShops := protected.Group("/merchant-shops")
		{
			merchantShops.GET("", handlers.GetMerchantShops)
			merchantShops.POST("", handlers.CreateMerchantShop)
			merchantShops.GET("/:id", handlers.GetMerchantShop)
			merchantShops.GET("/slug/:slug", handlers.GetMerchantShopBySlug)
			merchantShops.PUT("/:id", handlers.UpdateMerchantShop)
			merchantShops.DELETE("/:id", handlers.DeleteMerchantShop)

			// Customer management
			merchantShops.GET("/:id/customers", handlers.GetShopCustomers)
			merchantShops.POST("/:id/customers", handlers.AddShopCustomer)
			merchantShops.POST("/:id/customers/:customerId/credits", handlers.AddShopCredit)

			// Credit code management
			merchantShops.GET("/:id/credit-codes", handlers.GetCreditCodes)
			merchantShops.POST("/:id/credit-codes", handlers.GenerateCreditCode)
			merchantShops.POST("/:id/credit-codes/qr", handlers.GenerateQRCode)

			// Transaction management
			merchantShops.GET("/:id/transactions", handlers.GetShopTransactions)
		}

		// Merchant credit statistics
		protected.GET("/merchant/credit-stats", handlers.GetMerchantCreditStats)

		// Organization routes (redirect to unified shop routes)
		protected.GET("/organizations", handlers.GetOrganizations)
		protected.POST("/organizations", handlers.CreateOrganization)
		protected.GET("/organizations/:slug", handlers.GetOrganizationBySlug)
		protected.PUT("/organizations/:slug", handlers.UpdateOrganization)
		protected.DELETE("/organizations/:slug", handlers.DeleteOrganization)

		// Legacy organization endpoints (for backward compatibility)
		protected.GET("/organizations/id/:id", handlers.GetOrganization)

		// Branch management (redirect to unified shop branch routes)
		protected.GET("/org-branches", handlers.GetBranches)
		protected.POST("/org-branches", handlers.CreateBranch)
		protected.GET("/org-branches/:id", handlers.GetBranch)
		protected.PUT("/org-branches/:id", handlers.UpdateBranch)
		protected.DELETE("/org-branches/:id", handlers.DeleteBranch)

		// External user management (redirect to unified shop external user routes)
		protected.GET("/org-users", handlers.GetExternalUsers)
		protected.POST("/org-users", handlers.CreateExternalUser)
		protected.GET("/org-users/:id", handlers.GetExternalUser)
		protected.PUT("/org-users/:id", handlers.UpdateExternalUser)
		protected.DELETE("/org-users/:id", handlers.DeleteExternalUser)
		protected.POST("/org-users/:id/credits/add", handlers.AddCreditsToExternalUser)
		protected.POST("/org-users/:id/credits/reduce", handlers.ReduceCreditsFromExternalUser)
	}
}
