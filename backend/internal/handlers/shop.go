package handlers

import (
	"net/http"
	"regexp"
	"strings"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/adc-credit/backend/internal/utils"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// GetShops returns all shops for the current user
func GetShops(c *gin.Context) {
	user := c.MustGet("user").(models.User)

	var shops []models.Shop
	if user.Role == "admin" {
		// Admin can see all shops
		if err := database.DB.Find(&shops).Error; err != nil {
			c.J<PERSON>(http.StatusInternalServerError, gin.H{"error": "Failed to fetch shops"})
			return
		}
	} else {
		// Regular users can only see shops they own
		if err := database.DB.Where("owner_user_id = ?", user.ID).Find(&shops).Error; err != nil {
			c.J<PERSON>N(http.StatusInternalServerError, gin.H{"error": "Failed to fetch shops"})
			return
		}
	}

	c.<PERSON>(http.StatusOK, shops)
}

// GetShop returns a specific shop by ID
func GetShop(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")

	var shop models.Shop
	if user.Role == "admin" {
		// Admin can see any shop
		if err := database.DB.First(&shop, "id = ?", shopID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
			return
		}
	} else {
		// Regular users can only see shops they own
		if err := database.DB.First(&shop, "id = ? AND owner_user_id = ?", shopID, user.ID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
			return
		}
	}

	c.JSON(http.StatusOK, shop)
}

// GetShopBySlug returns a specific shop by slug
func GetShopBySlug(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopSlug := c.Param("slug")

	var shop models.Shop
	if user.Role == "admin" {
		// Admin can see any shop
		if err := database.DB.First(&shop, "slug = ?", shopSlug).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
			return
		}
	} else {
		// Regular users can only see shops they own
		if err := database.DB.First(&shop, "slug = ? AND owner_user_id = ?", shopSlug, user.ID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
			return
		}
	}

	c.JSON(http.StatusOK, shop)
}

// CreateShop creates a new shop
func CreateShop(c *gin.Context) {
	user := c.MustGet("user").(models.User)

	type CreateShopRequest struct {
		Name         string `json:"name" binding:"required"`
		Description  string `json:"description"`
		ShopType     string `json:"shop_type"` // "retail", "api_service", "enterprise"
		ContactEmail string `json:"contact_email"`
		ContactPhone string `json:"contact_phone"`
	}

	var req CreateShopRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate shop type
	validShopTypes := []string{"retail", "api_service", "enterprise"}
	if req.ShopType == "" {
		req.ShopType = "retail" // Default to retail
	}

	isValidType := false
	for _, validType := range validShopTypes {
		if req.ShopType == validType {
			isValidType = true
			break
		}
	}
	if !isValidType {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop type. Must be one of: retail, api_service, enterprise"})
		return
	}

	shopID := uuid.New()
	slug := generateShopSlug(req.Name)
	slug = ensureUniqueShopSlug(slug, shopID)

	shop := models.Shop{
		ID:           shopID,
		Slug:         slug,
		Name:         req.Name,
		Description:  req.Description,
		ShopType:     req.ShopType,
		ContactEmail: req.ContactEmail,
		ContactPhone: req.ContactPhone,
		OwnerUserID:  user.ID,
	}

	if err := database.DB.Create(&shop).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create shop"})
		return
	}

	c.JSON(http.StatusCreated, shop)
}

// UpdateShop updates an existing shop
func UpdateShop(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")

	var shop models.Shop
	if err := database.DB.First(&shop, "id = ? AND owner_user_id = ?", shopID, user.ID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
		return
	}

	type UpdateShopRequest struct {
		Name         string `json:"name"`
		Description  string `json:"description"`
		ShopType     string `json:"shop_type"`
		ContactEmail string `json:"contact_email"`
		ContactPhone string `json:"contact_phone"`
	}

	var req UpdateShopRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Update fields if provided
	if req.Name != "" {
		shop.Name = req.Name
		// Regenerate slug if name changed
		newSlug := generateShopSlug(req.Name)
		if newSlug != shop.Slug {
			shop.Slug = ensureUniqueShopSlug(newSlug, shop.ID)
		}
	}
	if req.Description != "" {
		shop.Description = req.Description
	}
	if req.ShopType != "" {
		// Validate shop type
		validShopTypes := []string{"retail", "api_service", "enterprise"}
		isValidType := false
		for _, validType := range validShopTypes {
			if req.ShopType == validType {
				isValidType = true
				break
			}
		}
		if !isValidType {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop type. Must be one of: retail, api_service, enterprise"})
			return
		}
		shop.ShopType = req.ShopType
	}
	if req.ContactEmail != "" {
		shop.ContactEmail = req.ContactEmail
	}
	if req.ContactPhone != "" {
		shop.ContactPhone = req.ContactPhone
	}

	if err := database.DB.Save(&shop).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update shop"})
		return
	}

	c.JSON(http.StatusOK, shop)
}

// DeleteShop deletes a shop
func DeleteShop(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")

	var shop models.Shop
	if err := database.DB.First(&shop, "id = ? AND owner_user_id = ?", shopID, user.ID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
		return
	}

	if err := database.DB.Delete(&shop).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete shop"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Shop deleted successfully"})
}

// Helper functions

func generateShopSlug(name string) string {
	// Convert to lowercase
	slug := strings.ToLower(name)

	// Replace spaces and special characters with hyphens
	reg := regexp.MustCompile(`[^a-z0-9]+`)
	slug = reg.ReplaceAllString(slug, "-")

	// Remove leading and trailing hyphens
	slug = strings.Trim(slug, "-")

	// If empty, use a default
	if slug == "" {
		slug = "shop"
	}

	return slug
}

func ensureUniqueShopSlug(baseSlug string, excludeID uuid.UUID) string {
	return utils.EnsureUniqueSlug(baseSlug, func(s string) bool {
		var count int64
		database.DB.Model(&models.Shop{}).Where("slug = ? AND id != ?", s, excludeID).Count(&count)
		return count > 0
	})
}
